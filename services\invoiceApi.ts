// services/invoiceApi.ts
import { useAuthStore } from '~/stores/auth'
import { useRuntimeConfig } from '#app'

// Invoice Types
export interface InvoiceItem {
  _id?: string
  description: string
  quantity: number
  unitPrice: number
  total?: number
  type?: 'product' | 'service'
  productId?: string
  serviceId?: string
}

export interface Invoice {
  _id?: string
  userId?: string
  invoiceNumber?: string
  clientName: string
  clientEmail: string
  clientPhone?: string
  clientAddress?: {
    street?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  items: InvoiceItem[]
  subtotal?: number
  taxRate?: number
  taxAmount?: number
  discountRate?: number
  discountAmount?: number
  total?: number
  status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  isPaid?: boolean
  issueDate?: string
  dueDate: string
  notes?: string
  paymentTerms?: string
  invoiceType?: 'product' | 'service' | 'mixed'
  createdAt?: string
  updatedAt?: string
}

export interface InvoicesResponse {
  success: boolean
  count: number
  data: Invoice[]
  pagination?: {
    page: number
    limit: number
    totalPages: number
    totalInvoices: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface InvoiceResponse {
  success: boolean
  data: Invoice
  message?: string
}

export interface InvoiceStats {
  totalInvoices: number
  totalAmount: number
  paidAmount: number
  pendingAmount: number
  overdueAmount: number
  draftCount: number
  sentCount: number
  paidCount: number
  overdueCount: number
}

export interface InvoiceStatsResponse {
  success: boolean
  data: InvoiceStats
}

export interface InvoiceParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  clientName?: string
  invoiceType?: string
  startDate?: string
  endDate?: string
  sort?: string
}

class InvoiceApiService {
  private _baseURL: string | null = null

  private get baseURL(): string {
    if (!this._baseURL) {
      const config = useRuntimeConfig()
      this._baseURL = config.public.apiBaseUrl || 'http://localhost:5000/api'
    }
    return this._baseURL
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // Get all invoices
  async getInvoices(params?: InvoiceParams): Promise<InvoicesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/invoices${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching invoices with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<InvoicesResponse>(response)
    } catch (error) {
      console.error('Get invoices error:', error)
      throw error
    }
  }

  // Get single invoice
  async getInvoice(id: string): Promise<InvoiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${id}`

      console.log('🔗 API: Fetching invoice with URL:', url)
      console.log('🔗 API: Headers:', headers)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      console.log('🔗 API: Response status:', response.status)
      console.log('🔗 API: Response ok:', response.ok)

      const result = await this.handleResponse<InvoiceResponse>(response)
      console.log('🔗 API: Parsed response:', result)

      return result
    } catch (error) {
      console.error('❌ API: Get invoice error:', error)
      throw error
    }
  }

  // Create new invoice
  async createInvoice(invoiceData: Omit<Invoice, '_id' | 'userId'>): Promise<InvoiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices`

      console.log('🔗 Creating invoice with URL:', url)
      console.log('📝 Invoice data:', invoiceData)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(invoiceData)
      })
      
      return this.handleResponse<InvoiceResponse>(response)
    } catch (error) {
      console.error('Create invoice error:', error)
      throw error
    }
  }

  // Update invoice
  async updateInvoice(id: string, invoiceData: Partial<Invoice>): Promise<InvoiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${id}`

      console.log('🔗 Updating invoice with URL:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(invoiceData)
      })
      
      return this.handleResponse<InvoiceResponse>(response)
    } catch (error) {
      console.error('Update invoice error:', error)
      throw error
    }
  }

  // Delete invoice
  async deleteInvoice(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${id}`

      console.log('🔗 Deleting invoice with URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<{ success: boolean; message: string }>(response)
    } catch (error) {
      console.error('Delete invoice error:', error)
      throw error
    }
  }

  // Get invoice statistics
  async getInvoiceStats(): Promise<InvoiceStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/stats`

      console.log('🔗 Fetching invoice stats with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      return this.handleResponse<InvoiceStatsResponse>(response)
    } catch (error) {
      console.error('Get invoice stats error:', error)
      throw error
    }
  }

  // Update payment status
  async updatePaymentStatus(invoiceId: string, isPaid: boolean): Promise<InvoiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${invoiceId}/payment-status`

      console.log('🔗 Updating payment status with URL:', url)

      const response = await fetch(url, {
        method: 'PATCH',
        headers,
        body: JSON.stringify({ isPaid })
      })

      return this.handleResponse<InvoiceResponse>(response)
    } catch (error) {
      console.error('Update payment status error:', error)
      throw error
    }
  }

  // Generate PDF
  async generatePDF(invoiceId: string): Promise<InvoiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${invoiceId}/pdf`

      console.log('🔗 Generating PDF with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      return this.handleResponse<InvoiceResponse>(response)
    } catch (error) {
      console.error('Generate PDF error:', error)
      throw error
    }
  }

  // Send email
  async sendEmail(invoiceId: string, emailData: { subject?: string; message?: string; recipientEmail?: string }): Promise<any> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/invoices/${invoiceId}/send-email`

      console.log('🔗 Sending email with URL:', url)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(emailData)
      })

      return this.handleResponse<any>(response)
    } catch (error) {
      console.error('Send email error:', error)
      throw error
    }
  }
}

export const invoiceApi = new InvoiceApiService()
export type { Invoice, InvoiceItem, InvoicesResponse, InvoiceResponse, InvoiceStats, InvoiceParams }

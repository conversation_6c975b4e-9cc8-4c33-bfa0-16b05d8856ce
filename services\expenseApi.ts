// Types for Expense API
export interface Expense {
  _id: string
  userId: string
  expenseId: string
  vendor: string
  category: string
  description: string
  amount: number
  currency: string
  date: string
  status: 'pending' | 'approved' | 'rejected' | 'paid'
  paymentMethod: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'other'
  receiptUrl?: string
  notes?: string
  tags: string[]
  isRecurring: boolean
  recurringFrequency?: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  nextRecurringDate?: string
  taxRate: number
  taxAmount: number
  totalAmount: number
  approvedBy?: string
  approvedAt?: string
  rejectionReason?: string
  createdAt: string
  updatedAt: string
}

export interface ExpenseStats {
  totalExpenses: number
  totalCount: number
  pendingAmount: number
  approvedAmount: number
  paidAmount: number
  rejectedAmount: number
  pendingCount: number
  approvedCount: number
  paidCount: number
  rejectedCount: number
}

export interface ExpensesResponse {
  success: boolean
  count: number
  totalExpenses: number
  totalPages: number
  currentPage: number
  data: Expense[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalExpenses: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface ExpenseResponse {
  success: boolean
  data: Expense
  message?: string
}

export interface ExpenseStatsResponse {
  success: boolean
  data: ExpenseStats
}

export interface ExpenseCategoriesResponse {
  success: boolean
  data: string[]
}

export interface ExpenseParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
  sort?: string
  startDate?: string
  endDate?: string
}

class ExpenseApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getExpenses(params?: ExpenseParams): Promise<ExpensesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/expenses${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching expenses with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ExpensesResponse>(response)
    } catch (error) {
      console.error('Get expenses error:', error)
      throw error
    }
  }

  async getExpense(id: string): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/${id}`

      console.log('🔗 Fetching expense with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Get expense error:', error)
      throw error
    }
  }

  async createExpense(expenseData: Omit<Expense, '_id' | 'userId' | 'expenseId' | 'createdAt' | 'updatedAt'>): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses`

      console.log('🔗 Creating expense with URL:', url)
      console.log('📊 Expense data:', expenseData)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(expenseData)
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Create expense error:', error)
      throw error
    }
  }

  async updateExpense(id: string, expenseData: Partial<Expense>): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/${id}`

      console.log('🔗 Updating expense with URL:', url)
      console.log('📊 Expense data:', expenseData)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(expenseData)
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Update expense error:', error)
      throw error
    }
  }

  async deleteExpense(id: string): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/${id}`

      console.log('🔗 Deleting expense with URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Delete expense error:', error)
      throw error
    }
  }

  async getExpenseStats(startDate?: string, endDate?: string): Promise<ExpenseStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (startDate) queryParams.append('startDate', startDate)
      if (endDate) queryParams.append('endDate', endDate)
      
      const url = `${this.baseURL}/expenses/stats${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching expense stats with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ExpenseStatsResponse>(response)
    } catch (error) {
      console.error('Get expense stats error:', error)
      throw error
    }
  }

  async getExpenseCategories(): Promise<ExpenseCategoriesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/categories`

      console.log('🔗 Fetching expense categories with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ExpenseCategoriesResponse>(response)
    } catch (error) {
      console.error('Get expense categories error:', error)
      throw error
    }
  }

  async approveExpense(id: string): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/${id}/approve`

      console.log('🔗 Approving expense with URL:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Approve expense error:', error)
      throw error
    }
  }

  async rejectExpense(id: string, reason: string): Promise<ExpenseResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/expenses/${id}/reject`

      console.log('🔗 Rejecting expense with URL:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ reason })
      })
      
      return this.handleResponse<ExpenseResponse>(response)
    } catch (error) {
      console.error('Reject expense error:', error)
      throw error
    }
  }
}

export const expenseApi = new ExpenseApiService()

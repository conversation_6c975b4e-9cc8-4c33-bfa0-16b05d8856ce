// Types for Dashboard API
export interface DashboardOverview {
  clients: {
    total: number
    active: number
  }
  invoices: {
    total: number
    paid: number
    totalRevenue: number
    paidRevenue: number
    pendingRevenue: number
  }
  quotes: {
    total: number
    accepted: number
    totalValue: number
    acceptedValue: number
  }
  expenses: {
    total: number
    pending: number
    approved: number
  }
  products: {
    total: number
    active: number
    totalValue: number
    lowStock: number
  }
  services: {
    total: number
    active: number
    totalRevenue: number
  }
  financial: {
    totalRevenue: number
    netIncome: number
    pendingPayments: number
    totalExpenses: number
  }
}

export interface RecentActivity {
  type: 'invoice' | 'quote' | 'expense' | 'client'
  id: string
  title: string
  description: string
  status: string
  date: string
}

export interface DashboardOverviewResponse {
  success: boolean
  data: DashboardOverview
}

export interface RecentActivitiesResponse {
  success: boolean
  data: RecentActivity[]
}

class DashboardApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getDashboardOverview(): Promise<DashboardOverviewResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/dashboard/overview`

      console.log('🔗 Fetching dashboard overview with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<DashboardOverviewResponse>(response)
    } catch (error) {
      console.error('Get dashboard overview error:', error)
      throw error
    }
  }

  async getRecentActivities(limit: number = 10): Promise<RecentActivitiesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/dashboard/activities?limit=${limit}`

      console.log('🔗 Fetching recent activities with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<RecentActivitiesResponse>(response)
    } catch (error) {
      console.error('Get recent activities error:', error)
      throw error
    }
  }
}

export const dashboardApi = new DashboardApiService()

// Types for Service API
export interface ServiceImage {
  url: string
  alt?: string
  isPrimary: boolean
}

export interface ServiceDuration {
  value?: number
  unit: 'hours' | 'days' | 'weeks' | 'months'
}

export interface Service {
  _id: string
  userId: string
  serviceId: string
  name: string
  description?: string
  category: string
  pricingType: 'fixed' | 'hourly' | 'daily' | 'monthly' | 'project'
  price: number
  currency: string
  estimatedDuration?: ServiceDuration
  isAvailable: boolean
  taxRate: number
  taxInclusive: boolean
  status: 'active' | 'inactive' | 'discontinued'
  deliverables: string[]
  requirements: string[]
  images: ServiceImage[]
  tags: string[]
  notes?: string
  totalOrders: number
  totalRevenue: number
  averageRating: number
  totalReviews: number
  lastOrderDate?: string
  allowBooking: boolean
  advanceBookingDays: number
  maxConcurrentOrders: number
  currentActiveOrders: number
  createdAt: string
  updatedAt: string
}

export interface ServiceStats {
  totalServices: number
  activeServices: number
  inactiveServices: number
  availableServices: number
  totalOrders: number
  totalRevenue: number
  averagePrice: number
  averageRating: number
}

export interface ServicesResponse {
  success: boolean
  count: number
  totalServices: number
  totalPages: number
  currentPage: number
  data: Service[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalServices: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface ServiceResponse {
  success: boolean
  data: Service
  message?: string
}

export interface ServiceStatsResponse {
  success: boolean
  data: ServiceStats
}

export interface ServiceCategoriesResponse {
  success: boolean
  data: string[]
}

export interface ServiceParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
  pricingType?: string
  available?: boolean
  sort?: string
}

class ServiceApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getServices(params?: ServiceParams): Promise<ServicesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/services${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching services with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ServicesResponse>(response)
    } catch (error) {
      console.error('Get services error:', error)
      throw error
    }
  }

  async getService(id: string): Promise<ServiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/${id}`

      console.log('🔗 Fetching service with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ServiceResponse>(response)
    } catch (error) {
      console.error('Get service error:', error)
      throw error
    }
  }

  async createService(serviceData: Omit<Service, '_id' | 'userId' | 'serviceId' | 'totalOrders' | 'totalRevenue' | 'averageRating' | 'totalReviews' | 'currentActiveOrders' | 'createdAt' | 'updatedAt'>): Promise<ServiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services`

      console.log('🔗 Creating service with URL:', url)
      console.log('📊 Service data:', serviceData)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(serviceData)
      })
      
      return this.handleResponse<ServiceResponse>(response)
    } catch (error) {
      console.error('Create service error:', error)
      throw error
    }
  }

  async updateService(id: string, serviceData: Partial<Service>): Promise<ServiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/${id}`

      console.log('🔗 Updating service with URL:', url)
      console.log('📊 Service data:', serviceData)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(serviceData)
      })
      
      return this.handleResponse<ServiceResponse>(response)
    } catch (error) {
      console.error('Update service error:', error)
      throw error
    }
  }

  async deleteService(id: string): Promise<ServiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/${id}`

      console.log('🔗 Deleting service with URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<ServiceResponse>(response)
    } catch (error) {
      console.error('Delete service error:', error)
      throw error
    }
  }

  async getServiceStats(): Promise<ServiceStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/stats`

      console.log('🔗 Fetching service stats with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ServiceStatsResponse>(response)
    } catch (error) {
      console.error('Get service stats error:', error)
      throw error
    }
  }

  async getServiceCategories(): Promise<ServiceCategoriesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/categories`

      console.log('🔗 Fetching service categories with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<ServiceCategoriesResponse>(response)
    } catch (error) {
      console.error('Get service categories error:', error)
      throw error
    }
  }

  async toggleServiceAvailability(id: string): Promise<ServiceResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/services/${id}/availability`

      console.log('🔗 Toggling service availability with URL:', url)

      const response = await fetch(url, {
        method: 'PUT',
        headers
      })
      
      return this.handleResponse<ServiceResponse>(response)
    } catch (error) {
      console.error('Toggle service availability error:', error)
      throw error
    }
  }
}

export const serviceApi = new ServiceApiService()

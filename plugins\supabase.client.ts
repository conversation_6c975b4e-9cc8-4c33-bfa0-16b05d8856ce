// plugins/supabase.client.ts
export default defineNuxtPlugin(async () => {
  const supabase = useSupabaseClient()

  // Configure Supabase client for better CORS handling
  if (process.client) {
    console.log('🔧 Supabase client plugin initialized')

    // Note: Auth state listener is now handled in the auth store to avoid conflicts
    // This plugin only handles client-side initialization

    // Handle initial session recovery
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.warn('⚠️ Session recovery error:', error.message)
        // Don't throw error, just log it
      }
      
      if (session) {
        console.log('🔄 Session recovered for:', session.user?.email)
      }
    } catch (error) {
      console.warn('⚠️ Failed to recover session:', error)
      // Don't throw error in plugin
    }
  }
})

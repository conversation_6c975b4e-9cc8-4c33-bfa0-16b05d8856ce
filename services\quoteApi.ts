// Types for Quote API
export interface QuoteItem {
  description: string
  quantity: number
  unitPrice: number
  total: number
}

export interface Quote {
  _id: string
  userId: string
  quoteNumber: string
  clientId: string
  clientName: string
  clientEmail: string
  clientAddress?: {
    street?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  items: QuoteItem[]
  subtotal: number
  discount: number
  discountAmount: number
  taxRate: number
  taxAmount: number
  total: number
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired'
  issueDate: string
  validUntil: string
  notes?: string
  terms?: string
  createdAt: string
  updatedAt: string
}

export interface QuoteStats {
  totalQuotes: number
  draftQuotes: number
  sentQuotes: number
  acceptedQuotes: number
  rejectedQuotes: number
  expiredQuotes: number
  totalValue: number
  acceptedValue: number
  averageValue: number
}

export interface QuotesResponse {
  success: boolean
  count: number
  totalQuotes: number
  totalPages: number
  currentPage: number
  data: Quote[]
  pagination: {
    page: number
    limit: number
    totalPages: number
    totalQuotes: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface QuoteResponse {
  success: boolean
  data: Quote
  message?: string
}

export interface QuoteStatsResponse {
  success: boolean
  data: QuoteStats
}

export interface QuoteParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  sort?: string
}

class QuoteApiService {
  private get baseURL(): string {
    const config = useRuntimeConfig()
    return config.public.apiBaseUrl
  }
  
  private async getAuthHeaders() {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    return data
  }

  async getQuotes(params?: QuoteParams): Promise<QuotesResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const queryParams = new URLSearchParams()
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString())
          }
        })
      }
      
      const url = `${this.baseURL}/quotes${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('🔗 Fetching quotes with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<QuotesResponse>(response)
    } catch (error) {
      console.error('Get quotes error:', error)
      throw error
    }
  }

  async getQuote(id: string): Promise<QuoteResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes/${id}`

      console.log('🔗 Fetching quote with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<QuoteResponse>(response)
    } catch (error) {
      console.error('Get quote error:', error)
      throw error
    }
  }

  async createQuote(quoteData: Omit<Quote, '_id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<QuoteResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes`

      console.log('🔗 Creating quote with URL:', url)
      console.log('📊 Quote data:', quoteData)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(quoteData)
      })
      
      return this.handleResponse<QuoteResponse>(response)
    } catch (error) {
      console.error('Create quote error:', error)
      throw error
    }
  }

  async updateQuote(id: string, quoteData: Partial<Quote>): Promise<QuoteResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes/${id}`

      console.log('🔗 Updating quote with URL:', url)
      console.log('📊 Quote data:', quoteData)

      const response = await fetch(url, {
        method: 'PUT',
        headers,
        body: JSON.stringify(quoteData)
      })
      
      return this.handleResponse<QuoteResponse>(response)
    } catch (error) {
      console.error('Update quote error:', error)
      throw error
    }
  }

  async deleteQuote(id: string): Promise<QuoteResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes/${id}`

      console.log('🔗 Deleting quote with URL:', url)

      const response = await fetch(url, {
        method: 'DELETE',
        headers
      })
      
      return this.handleResponse<QuoteResponse>(response)
    } catch (error) {
      console.error('Delete quote error:', error)
      throw error
    }
  }

  async getQuoteStats(): Promise<QuoteStatsResponse> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes/stats`

      console.log('🔗 Fetching quote stats with URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })
      
      return this.handleResponse<QuoteStatsResponse>(response)
    } catch (error) {
      console.error('Get quote stats error:', error)
      throw error
    }
  }

  async convertToInvoice(id: string, dueDate?: string): Promise<any> {
    try {
      const headers = await this.getAuthHeaders()
      const url = `${this.baseURL}/quotes/${id}/convert`

      console.log('🔗 Converting quote to invoice with URL:', url)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify({ dueDate })
      })
      
      return this.handleResponse<any>(response)
    } catch (error) {
      console.error('Convert quote to invoice error:', error)
      throw error
    }
  }
}

export const quoteApi = new QuoteApiService()

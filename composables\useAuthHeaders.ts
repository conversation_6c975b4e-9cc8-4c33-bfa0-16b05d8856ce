// composables/useAuthHeaders.ts
import { useAuthStore } from '~/stores/auth'

/**
 * Standardized authentication headers utility
 * Used across all API services to ensure consistent token handling
 */
export const useAuthHeaders = () => {
  const authStore = useAuthStore()

  /**
   * Get authentication headers with automatic token refresh
   * @returns Promise<HeadersInit> - Headers object with Authorization and Content-Type
   * @throws Error if authentication fails or token cannot be refreshed
   */
  const getAuthHeaders = async (): Promise<HeadersInit> => {
    // Ensure auth store is initialized
    if (!authStore.initialized) {
      console.log('🔄 Initializing auth store for API request...')
      await authStore.fetchUser()
    }

    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      throw new Error('Authentication required. Please log in.')
    }

    // Get current token
    let token = authStore.session?.access_token
    
    if (!token) {
      throw new Error('No authentication token available. Please log in again.')
    }

    // Check if token is expired or will expire soon (within 5 minutes)
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = authStore.session?.expires_at || 0
    const bufferTime = 5 * 60 // 5 minutes buffer
    
    if (expiresAt < (now + bufferTime)) {
      console.log('🔄 Token expired or expiring soon, attempting refresh...')
      
      try {
        const refreshed = await authStore.refreshSession()
        
        if (!refreshed || !authStore.session?.access_token) {
          console.error('❌ Token refresh failed')
          await authStore.logout()
          throw new Error('Session expired. Please log in again.')
        }
        
        token = authStore.session.access_token
        console.log('✅ Token refreshed successfully')
      } catch (error) {
        console.error('❌ Error refreshing token:', error)
        await authStore.logout()
        throw new Error('Session expired. Please log in again.')
      }
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  }

  /**
   * Get basic headers without authentication
   * @returns HeadersInit - Basic headers for public endpoints
   */
  const getBasicHeaders = (): HeadersInit => {
    return {
      'Content-Type': 'application/json'
    }
  }

  /**
   * Check if user is authenticated and token is valid
   * @returns boolean - True if authenticated with valid token
   */
  const isAuthenticated = (): boolean => {
    if (!authStore.isAuthenticated || !authStore.session?.access_token) {
      return false
    }

    // Check token expiration
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = authStore.session?.expires_at || 0
    
    return expiresAt > now
  }

  /**
   * Handle authentication errors from API responses
   * @param error - Error object from API response
   * @param response - Response object (optional)
   */
  const handleAuthError = async (error: any, response?: Response) => {
    if (response?.status === 401 || error.message?.includes('401')) {
      console.log('🚫 Authentication error detected, logging out...')
      await authStore.logout()
      throw new Error('Authentication failed. Please log in again.')
    }
    
    if (response?.status === 403) {
      throw new Error('Access denied. You do not have permission to perform this action.')
    }
    
    throw error
  }

  return {
    getAuthHeaders,
    getBasicHeaders,
    isAuthenticated,
    handleAuthError
  }
}

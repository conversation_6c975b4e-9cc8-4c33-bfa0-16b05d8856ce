<template>
  <div class="min-h-screen bg-gray-50">
    <div class="p-8 space-y-8 max-w-7xl mx-auto">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
      <div class="space-y-2">
        <h1 class="text-3xl font-bold text-gray-900">Client Management</h1>
        <p class="text-gray-600 text-lg">Manage your clients and their information</p>
      </div>
      <Button @click="openCreateModal" class="bg-[#00C951] hover:bg-[#00B048] px-6 py-3 text-base font-medium">
        <Icon name="lucide:plus" class="w-5 h-5 mr-2" />
        Add New Client
      </Button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card class="hover:shadow-lg transition-shadow duration-200">
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <p class="text-sm font-medium text-gray-600">Total Clients</p>
              <p class="text-3xl font-bold text-gray-900">{{ clientStore.stats.totalClients }}</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-xl">
              <Icon name="lucide:users" class="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card class="hover:shadow-lg transition-shadow duration-200">
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <p class="text-sm font-medium text-gray-600">Active Clients</p>
              <p class="text-3xl font-bold text-green-600">{{ clientStore.stats.activeClients }}</p>
            </div>
            <div class="p-3 bg-green-100 rounded-xl">
              <Icon name="lucide:user-check" class="w-8 h-8 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card class="hover:shadow-lg transition-shadow duration-200">
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <p class="text-sm font-medium text-gray-600">Total Billed</p>
              <p class="text-3xl font-bold text-purple-600">₹{{ formatCurrency(clientStore.stats.totalInvoiceAmount) }}</p>
            </div>
            <div class="p-3 bg-purple-100 rounded-xl">
              <Icon name="lucide:indian-rupee" class="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card class="hover:shadow-lg transition-shadow duration-200">
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="space-y-2">
              <p class="text-sm font-medium text-gray-600">Outstanding</p>
              <p class="text-3xl font-bold text-orange-600">₹{{ formatCurrency(clientStore.stats.totalOutstanding) }}</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-xl">
              <Icon name="lucide:clock" class="w-8 h-8 text-orange-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Filters and Search -->
    <Card>
      <CardContent class="p-6">
        <div class="flex flex-col sm:flex-row gap-6">
          <div class="flex-1">
            <Input
              v-model="searchTerm"
              placeholder="Search clients by name, email, or company..."
              class="w-full text-base py-3"
              @input="debouncedSearch"
            >
              <template #prefix>
                <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
              </template>
            </Input>
          </div>
          <div class="flex gap-3 items-center">
            <select
              v-model="statusFilter"
              @change="filterClients"
              class="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00C951] text-base min-w-[140px]"
            >
              <option value="">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Suspended">Suspended</option>
            </select>
            <Button variant="outline" @click="resetFilters" class="px-4 py-3">
              <Icon name="lucide:filter-x" class="w-4 h-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Clients Table -->
    <Card>
      <CardContent class="p-0">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 border-b">
              <tr>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Client ID</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Company</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="px-8 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created</th>
                
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="clientStore.isLoading">
                <td colspan="8" class="px-8 py-12 text-center text-gray-500">
                  <Icon name="lucide:loader-2" class="w-8 h-8 animate-spin mx-auto mb-3" />
                  <p class="text-base font-medium">Loading clients...</p>
                </td>
              </tr>
              <tr v-else-if="clientStore.clients.length === 0">
                <td colspan="8" class="px-8 py-16 text-center text-gray-500">
                  <Icon name="lucide:users" class="w-16 h-16 mx-auto mb-6 text-gray-300" />
                  <p class="text-xl font-semibold text-gray-700 mb-2">No clients found</p>
                  <p class="text-base text-gray-500">Get started by adding your first client</p>
                </td>
              </tr>
              <tr v-else v-for="client in clientStore.clients" :key="client._id" class="hover:bg-gray-50 transition-colors duration-150">
                <td class="px-8 py-6 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-12 w-12">
                      <div class="h-12 w-12 rounded-full bg-[#00C951] flex items-center justify-center shadow-sm">
                        <span class="text-base font-semibold text-white">
                          {{ getInitials(client.name || 'U') }}
                        </span>
                      </div>
                    </div>
                    <div class="ml-5">
                      <div class="text-base font-semibold text-gray-900">{{ client.name }}</div>
                      <div class="text-sm text-gray-500">{{ client.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-8 py-6 whitespace-nowrap">
                  <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full" :class="getTypeClass(client.type)">
                    {{ client.type || 'Individual' }}
                  </span>
                </td>
                <td class="px-8 py-6 whitespace-nowrap">
                  <div class="text-sm font-mono text-gray-900 font-medium">{{ client.clientId || 'Generating...' }}</div>
                  <div class="text-xs text-gray-500">Auto-generated</div>
                </td>
                <td class="px-8 py-6 whitespace-nowrap">
                  <div class="text-sm text-gray-900 font-medium">{{ client.phone || 'N/A' }}</div>
                  <div class="text-sm text-gray-500">{{ client.address?.city || 'N/A' }}</div>
                </td>
                <td class="px-8 py-6 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ client.company?.name || 'Individual' }}</div>
                  <div class="text-sm text-gray-500">{{ client.company?.website || '' }}</div>
                </td>
                <td class="px-8 py-6 whitespace-nowrap">
                  <div class="relative group">
                    <button
                      @click="toggleClientStatus(client)"
                      :disabled="isUpdatingStatus"
                      class="px-3 py-1 inline-flex items-center text-sm font-semibold rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      :class="getStatusClass(client.status || 'Active')"
                    >
                      <Icon v-if="isUpdatingStatus && updatingClientId === client._id" name="lucide:loader-2" class="w-3 h-3 mr-1 animate-spin" />
                      <Icon v-else name="lucide:mouse-pointer-click" class="w-3 h-3 mr-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                      {{ client.status || 'Active' }}
                    </button>
                    <!-- Tooltip -->
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      Click to {{ (client.status || 'Active') === 'Active' ? 'deactivate' : 'activate' }}
                    </div>
                  </div>
                </td>
                <td class="px-8 py-6 whitespace-nowrap text-sm text-gray-500 font-medium">
                  {{ formatDate(client.createdAt) }}
                </td>
                <td class="px-8 py-6 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end gap-3">
                    <Button variant="ghost" size="sm" @click="editClient(client)" class="hover:bg-blue-50 hover:text-blue-600">
                      <Icon name="lucide:edit" class="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="viewClient(client)" class="hover:bg-green-50 hover:text-green-600">
                      <Icon name="lucide:eye" class="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" @click="deleteClientConfirm(client)" class="text-red-600 hover:text-red-700 hover:bg-red-50">
                      <Icon name="lucide:trash-2" class="w-4 h-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="clientStore.pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing {{ (clientStore.pagination.page - 1) * clientStore.pagination.limit + 1 }} to 
              {{ Math.min(clientStore.pagination.page * clientStore.pagination.limit, clientStore.pagination.totalClients) }} 
              of {{ clientStore.pagination.totalClients }} results
            </div>
            <div class="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                @click="clientStore.prevPage()" 
                :disabled="!clientStore.pagination.hasPrevPage"
              >
                Previous
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                @click="clientStore.nextPage()" 
                :disabled="!clientStore.pagination.hasNextPage"
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    

    <!-- Client Modal -->
    <ClientModal
      :open="showModal"
      :client="selectedClient"
      @close="closeModal"
      @client-saved="onClientSaved"
    />

    <!-- Delete Confirmation Modal -->
    <Dialog :open="showDeleteModal" @update:open="showDeleteModal = $event">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Client</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete "{{ clientToDelete?.name }}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="showDeleteModal = false">Cancel</Button>
          <Button variant="destructive" @click="confirmDelete" :disabled="isDeleting">
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import { useSearchManagement } from '~/composables/useSearchClear'
import type { Client } from '~/services/clientApi'

// Simple debounce function
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

definePageMeta({
  middleware: 'auth'
})

const clientStore = useClientStore()
const toast = useToast()
const { clearReactiveSearch } = useSearchManagement()

// Modal states
const showModal = ref(false)
const showDeleteModal = ref(false)
const selectedClient = ref<Client | null>(null)
const clientToDelete = ref<Client | null>(null)
const isDeleting = ref(false)

// Filter states
const searchTerm = ref('')
const statusFilter = ref('')

// Status toggle states
const isUpdatingStatus = ref(false)
const updatingClientId = ref<string | null>(null)

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount || 0)
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-IN')
}

const getInitials = (name: string) => {
  return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Active':
      return 'bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer'
    case 'Inactive':
      return 'bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer'
    case 'Suspended':
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 cursor-pointer'
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer'
  }
}

const getTypeClass = (type: string) => {
  switch (type) {
    case 'Business':
      return 'bg-blue-100 text-blue-800'
    case 'Individual':
    default:
      return 'bg-purple-100 text-purple-800'
  }
}

// Actions
const openCreateModal = () => {
  selectedClient.value = null
  showModal.value = true
}

const editClient = (client: Client) => {
  selectedClient.value = client
  showModal.value = true
}

const viewClient = (client: Client) => {
  // Navigate to client detail page
  navigateTo(`/clients/CustomerView/${client._id}`)
}

const deleteClientConfirm = (client: Client) => {
  clientToDelete.value = client
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!clientToDelete.value?._id) return

  try {
    isDeleting.value = true
    await clientStore.deleteClient(clientToDelete.value._id)
    toast.success('Client deleted successfully')
    showDeleteModal.value = false
    clientToDelete.value = null
  } catch (error: any) {
    toast.error('Failed to delete client: ' + error.message)
  } finally {
    isDeleting.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedClient.value = null
}

const onClientSaved = () => {
  closeModal()

  // Clear search field to prevent auto-population
  clearReactiveSearch(searchTerm)
  // Clear the store search filter as well
  clientStore.clearSearch()

  // Refresh the client list
  clientStore.fetchClients()
}

// Search and filter functions
const debouncedSearch = debounce(async () => {
  await clientStore.searchClients(searchTerm.value)
}, 300)

const filterClients = async () => {
  await clientStore.filterByStatus(statusFilter.value)
}

const resetFilters = async () => {
  searchTerm.value = ''
  statusFilter.value = ''
  await clientStore.resetFilters()
}

// Status toggle function
const toggleClientStatus = async (client: Client) => {
  if (isUpdatingStatus.value) return

  try {
    isUpdatingStatus.value = true
    updatingClientId.value = client._id

    // Determine new status
    const currentStatus = client.status || 'Active'
    const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active'

    // Update client status
    await clientStore.updateClient(client._id, { status: newStatus })

    // Show success message
    toast.success(`Client ${newStatus.toLowerCase()} successfully`)

    // Refresh stats to update the counters
    await clientStore.fetchStats()

  } catch (error: any) {
    console.error('Error updating client status:', error)
    toast.error('Failed to update client status: ' + error.message)
  } finally {
    isUpdatingStatus.value = false
    updatingClientId.value = null
  }
}



// Initialize
onMounted(async () => {
  try {
    await clientStore.refreshClients()
  } catch (error: any) {
    toast.error('Failed to load clients: ' + error.message)
  }
})
</script>

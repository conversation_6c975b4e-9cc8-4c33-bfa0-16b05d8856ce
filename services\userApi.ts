import { useAuthStore } from '~/stores/auth'

// Types
export interface UserProfile {
  id: string
  email: string
  full_name: string
  avatar_url?: string
  phone?: string
  company?: string
  address?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  timezone: string
  currency: string
  language: string
  created_at: string
  updated_at: string
}

export interface UserSettings {
  user_id: string
  notifications_email: boolean
  notifications_browser: boolean
  auto_save_drafts: boolean
  default_currency: string
  default_tax_rate: number
  invoice_terms: string
  company_logo?: string
  invoice_template: string
  created_at: string
  updated_at: string
}

export interface UpdateProfileData {
  full_name?: string
  phone?: string
  company?: string
  address?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  timezone?: string
  currency?: string
  language?: string
}

export interface UpdatePasswordData {
  currentPassword: string
  newPassword: string
}

export interface ApiResponse<T> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

class UserApiService {
  private _baseURL: string | null = null

  private get baseURL(): string {
    if (!this._baseURL) {
      const config = useRuntimeConfig()
      this._baseURL = config.public.apiBaseUrl || 'http://localhost:5000/api'
    }
    return this._baseURL
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const { getAuthHeaders } = useAuthHeaders()
    return await getAuthHeaders()
  }

  // Get user profile
  async getUserProfile(): Promise<UserProfile> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/user/profile`, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<UserProfile> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.message || 'Failed to fetch user profile')
      }

      return result.data
    } catch (error) {
      console.error('Error fetching user profile:', error)
      throw error
    }
  }

  // Update user profile
  async updateUserProfile(profileData: UpdateProfileData): Promise<UserProfile> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/user/profile`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(profileData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<UserProfile> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.message || 'Failed to update user profile')
      }

      return result.data
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw error
    }
  }

  // Update password
  async updatePassword(passwordData: UpdatePasswordData): Promise<void> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/user/password`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(passwordData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<void> = await response.json()
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to update password')
      }
    } catch (error) {
      console.error('Error updating password:', error)
      throw error
    }
  }

  // Get user settings
  async getUserSettings(): Promise<UserSettings> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/user/settings`, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<UserSettings> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.message || 'Failed to fetch user settings')
      }

      return result.data
    } catch (error) {
      console.error('Error fetching user settings:', error)
      throw error
    }
  }

  // Update user settings
  async updateUserSettings(settingsData: Partial<UserSettings>): Promise<UserSettings> {
    try {
      const headers = await this.getAuthHeaders()
      
      const response = await fetch(`${this.baseURL}/user/settings`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(settingsData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<UserSettings> = await response.json()
      
      if (!result.success || !result.data) {
        throw new Error(result.message || 'Failed to update user settings')
      }

      return result.data
    } catch (error) {
      console.error('Error updating user settings:', error)
      throw error
    }
  }
}

// Export singleton instance
export const userApiService = new UserApiService()
export default userApiService

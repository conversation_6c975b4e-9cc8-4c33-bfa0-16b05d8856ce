<template>
    <!-- Recent Invoices -->
    <div class="bg-white text-black rounded-2xl shadow p-6">
        <h2 class="text-xl font-semibold mb-2">Recent Invoices</h2>
        <p class="text-sm text-gray-500 mb-6">Overview of your latest invoices</p>
        <div class="overflow-x-auto">
          <table class="min-w-full text-left text-sm">
            <thead class="border-b border-gray-200">
              <tr>
                <th class="py-2">Client</th>
                <th class="py-2">Invoice</th>
                <th class="py-2">Amount</th>
                <th class="py-2">Status</th>
              </tr>
            </thead>
            <tbody>
              <!-- Loading State -->
              <tr v-if="invoicesStore.isLoading">
                <td colspan="4" class="py-4 text-center text-gray-500">
                  <div class="flex justify-center items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-[#00C951] mr-2"></div>
                    Loading invoices...
                  </div>
                </td>
              </tr>

              <!-- Invoice Data -->
              <tr v-else-if="recentInvoices.length > 0" v-for="invoice in recentInvoices" :key="invoice._id" class="border-b border-gray-100">
                <td class="py-3 font-medium">{{ invoice.clientName }}</td>
                <td class="py-3">{{ invoice.invoiceNumber }}</td>
                <td class="py-3">${{ formatCurrency(invoice.total) }}</td>
                <td class="py-3">
                  <span
                    :class="[
                      'text-xs px-2 py-1 rounded-full font-semibold capitalize',
                      invoice.status === 'paid' && 'bg-green-100 text-green-700',
                      invoice.status === 'sent' && 'bg-blue-100 text-blue-700',
                      invoice.status === 'draft' && 'bg-gray-100 text-gray-700',
                      invoice.status === 'overdue' && 'bg-red-100 text-red-700',
                      invoice.status === 'cancelled' && 'bg-red-100 text-red-700'
                    ]"
                  >
                    {{ invoice.status }}
                  </span>
                </td>
              </tr>

              <!-- Empty State -->
              <tr v-else>
                <td colspan="4" class="py-8 text-center text-gray-500">
                  <div class="flex flex-col items-center">
                    <Icon name="lucide:file-text" class="w-8 h-8 text-gray-300 mb-2"/>
                    <p class="text-sm">No invoices found</p>
                    <NuxtLink to="/invoices/new" class="text-[#05DF72] text-sm mt-2 hover:underline">
                      Create your first invoice
                    </NuxtLink>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <NuxtLink to="/invoices" class="text-[#05DF72] text-sm inline-flex items-center gap-1 mt-6 hover:text-[#04C44A] transition-colors">
          View all invoices <ArrowRight class="w-4 h-4" />
        </NuxtLink>
      </div>
  </template>
  
  <script setup>
  import { computed } from 'vue'
  import { useInvoicesStore } from '~/stores/invoices'
  import { ArrowRight } from 'lucide-vue-next'

  const invoicesStore = useInvoicesStore()

  // Get recent invoices (last 4 for this component)
  const recentInvoices = computed(() => {
    return invoicesStore.invoices.slice(0, 4)
  })

  // Navigation function removed - this is a display-only overview

  // Helper function to format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  // Fetch recent invoices on component mount
  onMounted(async () => {
    try {
      if (invoicesStore.invoices.length === 0) {
        await invoicesStore.fetchInvoices({ limit: 4, sort: '-createdAt' })
      }
    } catch (error) {
      console.error('Failed to load recent invoices:', error)
    }
  })
  </script>
  
  <style scoped>
  table {
    width: 100%;
  }
  </style>
  
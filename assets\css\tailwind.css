@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));


@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Global Text Selection and Cursor Normalization */
  /* Prevent text selection on UI elements globally */
  * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: default;
  }

  /* Restore text selection for content and input elements */
  input,
  textarea,
  select,
  [contenteditable="true"],
  [contenteditable=""],
  .selectable-text,
  .prose,
  .prose *,
  p.selectable,
  span.selectable,
  div.content,
  div.selectable,
  .markdown-content,
  .markdown-content *,
  pre.selectable,
  code.selectable,
  .code-block,
  .text-content,
  .user-content,
  .editable-content {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    cursor: text !important;
  }

  /* Maintain pointer cursor for interactive elements */
  button,
  a,
  [role="button"],
  .cursor-pointer,
  label[for],
  select,
  input[type="checkbox"],
  input[type="radio"],
  input[type="submit"],
  input[type="button"],
  input[type="reset"],
  .clickable,
  [onclick],
  [data-clickable="true"],
  .btn-primary,
  .btn-secondary {
    cursor: pointer !important;
  }

  /* Text cursor for text input elements */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  input[type="number"],
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"],
  textarea {
    cursor: text !important;
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Disabled elements */
  button:disabled,
  input:disabled,
  select:disabled,
  textarea:disabled,
  [disabled] {
    cursor: not-allowed !important;
  }

  /* Loading states */
  .loading,
  .cursor-wait,
  [data-loading="true"] {
    cursor: wait !important;
  }

  /* Help cursor for elements with tooltips */
  [title]:not(input):not(textarea):not(select),
  .cursor-help,
  [data-tooltip] {
    cursor: help !important;
  }

  /* Move cursor for draggable elements */
  [draggable="true"],
  .draggable,
  .cursor-move {
    cursor: move !important;
  }

  /* Resize cursor for resizable elements */
  .resize,
  .cursor-resize {
    cursor: nw-resize !important;
  }
}
.btn-primary {
  @apply bg-green-500 text-white border font-bold py-3 px-8 rounded-lg font-serif shadow-lg hover:bg-white hover:text-black transition duration-300 ease-in-out;
}

.btn-secondary {
  @apply bg-transparent border font-bold border-green-400 text-black py-3 px-8 rounded-full font-serif hover:bg-green-300 hover:text-white transition duration-300 ease-in-out;
}

/* Utility classes for text selection control */
.select-none {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.select-text {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
}

.select-all {
  user-select: all !important;
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  -ms-user-select: all !important;
}

/* Cursor utility classes */
.cursor-default {
  cursor: default !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-text {
  cursor: text !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-wait {
  cursor: wait !important;
}

.cursor-help {
  cursor: help !important;
}

.cursor-move {
  cursor: move !important;
}

/* Special classes for Invoice Easy components */
.invoice-card {
  user-select: none;
  cursor: default;
}

.invoice-data {
  user-select: text;
  cursor: text;
}

.ui-element {
  user-select: none;
  cursor: default;
}

.content-text {
  user-select: text;
  cursor: text;
}

/* Specific overrides for Invoice Easy components */
.client-card,
.dashboard-card,
.stats-card,
.navigation-item,
.sidebar-item,
.menu-item,
.badge,
.status-badge,
.action-button {
  user-select: none !important;
  cursor: default !important;
}

.client-card button,
.dashboard-card button,
.stats-card button,
.action-button {
  cursor: pointer !important;
}

/* Form elements should always be selectable */
.form-input,
.form-textarea,
.form-select,
input.form-control,
textarea.form-control {
  user-select: text !important;
  cursor: text !important;
}

/* Table data that should be selectable */
.table-data,
.invoice-number,
.client-email,
.amount-display,
.date-display {
  user-select: text !important;
  cursor: text !important;
}

/* Prevent selection on table headers and UI elements */
.table-header,
.table-actions,
.button-group,
.icon-button {
  user-select: none !important;
  cursor: default !important;
}

.table-actions button,
.button-group button,
.icon-button {
  cursor: pointer !important;
}

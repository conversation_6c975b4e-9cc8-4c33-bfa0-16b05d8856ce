<template>
  <div class="min-h-screen bg-gray-100 py-8">
    <div class="max-w-4xl mx-auto">
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Text Selection & Cursor Behavior Test</h1>
        <p class="text-gray-600">Test page to verify global CSS text selection and cursor normalization</p>
      </div>
      
      <TextSelectionTest />
      
      <div class="mt-8 p-4 bg-white rounded-lg border">
        <h2 class="text-xl font-semibold mb-4">Additional Test Elements</h2>
        
        <!-- Navigation-like elements -->
        <nav class="mb-4">
          <ul class="flex space-x-4">
            <li><a href="#" class="navigation-item px-3 py-2 bg-blue-100 rounded">Nav Item 1</a></li>
            <li><a href="#" class="navigation-item px-3 py-2 bg-blue-100 rounded">Nav Item 2</a></li>
            <li><a href="#" class="navigation-item px-3 py-2 bg-blue-100 rounded">Nav Item 3</a></li>
          </ul>
        </nav>

        <!-- Sidebar-like elements -->
        <div class="flex gap-4 mb-4">
          <div class="w-48 bg-gray-50 p-4 rounded">
            <h3 class="sidebar-item font-semibold mb-2">Sidebar Menu</h3>
            <ul class="space-y-1">
              <li><a href="#" class="sidebar-item block px-2 py-1 hover:bg-gray-200 rounded">Dashboard</a></li>
              <li><a href="#" class="sidebar-item block px-2 py-1 hover:bg-gray-200 rounded">Clients</a></li>
              <li><a href="#" class="sidebar-item block px-2 py-1 hover:bg-gray-200 rounded">Invoices</a></li>
            </ul>
          </div>
          
          <div class="flex-1">
            <h3 class="font-semibold mb-2">Content Area</h3>
            <p class="content-text mb-2">This is a content area where text should be selectable. Users should be able to highlight and copy this text.</p>
            <div class="ui-element p-3 bg-gray-100 rounded mb-2">
              <span class="status-badge bg-green-100 text-green-800 px-2 py-1 rounded">Status: Active</span>
              <span class="ml-2">This UI element text should NOT be selectable</span>
            </div>
          </div>
        </div>

        <!-- Form elements test -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium mb-1">Email Address:</label>
            <input type="email" class="form-input w-full px-3 py-2 border rounded" placeholder="Enter email">
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Select Option:</label>
            <select class="form-select w-full px-3 py-2 border rounded">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'empty' // Use empty layout for testing
})
</script>

<style scoped>
/* Test-specific styles */
</style>

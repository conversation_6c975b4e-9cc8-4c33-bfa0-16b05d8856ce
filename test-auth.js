// test-auth.js - Simple authentication flow test
const baseUrl = 'http://localhost:5000/api';

async function testAuthFlow() {
  console.log('🧪 Starting Authentication Flow Test...\n');

  // Test 1: Public endpoint (should work)
  console.log('1️⃣ Testing public endpoint...');
  try {
    const response = await fetch(`${baseUrl}/auth-test/public`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Public endpoint test passed:', data.message);
    } else {
      console.log('❌ Public endpoint test failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Public endpoint test error:', error.message);
  }

  // Test 2: Protected endpoint without token (should fail)
  console.log('\n2️⃣ Testing protected endpoint without token...');
  try {
    const response = await fetch(`${baseUrl}/auth-test/protected`);
    const data = await response.json();
    
    if (response.status === 401) {
      console.log('✅ Protected endpoint correctly rejected request:', data.message);
    } else {
      console.log('❌ Protected endpoint should have rejected request but got:', response.status);
    }
  } catch (error) {
    console.log('❌ Protected endpoint test error:', error.message);
  }

  // Test 3: Protected endpoint with invalid token (should fail)
  console.log('\n3️⃣ Testing protected endpoint with invalid token...');
  try {
    const response = await fetch(`${baseUrl}/auth-test/protected`, {
      headers: {
        'Authorization': 'Bearer invalid-token-here'
      }
    });
    const data = await response.json();
    
    if (response.status === 401) {
      console.log('✅ Protected endpoint correctly rejected invalid token:', data.message);
    } else {
      console.log('❌ Protected endpoint should have rejected invalid token but got:', response.status);
    }
  } catch (error) {
    console.log('❌ Invalid token test error:', error.message);
  }

  // Test 4: Check CORS headers
  console.log('\n4️⃣ Testing CORS configuration...');
  try {
    const response = await fetch(`${baseUrl}/auth-test/public`, {
      method: 'OPTIONS'
    });
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
    };
    
    console.log('✅ CORS headers:', corsHeaders);
  } catch (error) {
    console.log('❌ CORS test error:', error.message);
  }

  console.log('\n🏁 Authentication Flow Test Complete!');
  console.log('\n📝 Summary:');
  console.log('- Public endpoints should be accessible without authentication');
  console.log('- Protected endpoints should reject requests without valid tokens');
  console.log('- Invalid tokens should be rejected');
  console.log('- CORS should be properly configured for frontend access');
  console.log('\n💡 To test with a valid token, log in through the frontend and check the browser console for the token.');
}

// Run the test
testAuthFlow().catch(console.error);

<template>
    <div>
      <!-- Heading -->
      <Heading
    title="Manage Invoices"
    description="Create and manage your invoices"
    icon="lucide:notepad-text"
    iconColor="text-green-400"
    bgColor="bg-white"
    :buttons="[
      {
        label: 'New Product Invoice',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/invoices/Create/product'
      },
      {
        label: 'New service Invoice',
        icon: 'lucide:circle-plus',
        bgColor: 'bg-[#00C951]',
        textColor: 'text-white',
        to: '/invoices/Create/service'
      },
    ]"
  />
      <!-- invoice transactions -->
       <div>
        <RevenueStats/>
       </div>
       <div>
        <Transations/>
       </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  definePageMeta({
    middleware: 'auth'
  })

  import type { ChatCompletionRequestMessage } from '~/utils/types';

  const prompt = ref('');
  const isLoading = ref(false);
  const messages = ref<ChatCompletionRequestMessage[]>([]);
  
  const submitForm = async () => {
    if (!prompt.value.trim()) return;
  
    isLoading.value = true;
  
    const userMessage: ChatCompletionRequestMessage = {
      role: 'user',
      content: prompt.value,
    };
  
    messages.value.push(userMessage);
    const userPrompt = prompt.value;
    prompt.value = '';
  
    // Simulate bot response after a delay
    setTimeout(() => {
      const botMessage: ChatCompletionRequestMessage = {
        role: 'assistant',
        content: `You asked: "${userPrompt}". Here's a simple explanation:\nTo calculate radius, use the formula:\nradius = diameter / 2.`,
      };
  
      messages.value.push(botMessage);
      isLoading.value = false;
    }, 1000);
  };
  </script>
  
  <style>
  /* Add any custom styles if needed */
  </style>
  
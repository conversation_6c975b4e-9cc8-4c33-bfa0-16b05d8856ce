<template>
  <div class="bg-white text-black space-y-8">
    <!-- Loading State -->
    <div v-if="dashboardStore.isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00C951]"></div>
      <span class="ml-2 text-gray-600">Loading dashboard...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="dashboardStore.hasError" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <Icon name="lucide:alert-circle" class="w-5 h-5 text-red-500 mr-2"/>
        <span class="text-red-700">{{ dashboardStore.error }}</span>
      </div>
      <button
        @click="refreshDashboard"
        class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
      >
        Try again
      </button>
    </div>

    <!-- Dashboard Content -->
    <div v-else>
      <!-- Top Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
        <!-- Clients Card -->
        <div>
          <ClientsCard/>
        </div>

        <!-- Products Card -->
        <div>
          <ProductCard/>
        </div>

        <!-- Service Card -->
        <div>
          <ServiceCard/>
        </div>
      </div>

      <!-- Bottom Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

          <!-- Total Revenue -->
        <div>
        <TotalRevenue/>
        </div>

        <!-- Total Due -->
        <div>
        <TotalDue/>
        </div>

        <!-- Product Revenue -->
        <div>
        <ProductRevenue/>
        </div>

        <!-- Service Revenue -->
        <div>
        <ServiceRevenue/>
        </div>

      </div>

      <!-- Recent Activities Section -->
      <div class="mt-12 mb-8">
        <h3 class="text-lg font-semibold mb-6">Recent Activities</h3>

        <!-- Loading State -->
        <div v-if="invoicesStore.isLoading" class="bg-white rounded-lg border shadow-sm p-8">
          <div class="flex justify-center items-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#00C951]"></div>
            <span class="ml-2 text-gray-600">Loading recent activities...</span>
          </div>
        </div>

        <!-- Recent Invoices Activities -->
        <div v-else-if="recentInvoices.length > 0" class="bg-white rounded-lg border shadow-sm">
          <div class="divide-y divide-gray-200">
            <div
              v-for="invoice in recentInvoices"
              :key="invoice._id"
              class="p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <Icon
                      name="lucide:file-text"
                      class="w-5 h-5 text-[#00C951]"
                    />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      Invoice {{ invoice.invoiceNumber }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ invoice.clientName }} • ${{ formatCurrency(invoice.total) }}
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <span
                    :class="getInvoiceStatusColor(invoice.status)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize"
                  >
                    {{ invoice.status }}
                  </span>
                  <p class="text-xs text-gray-500 mt-1">{{ formatInvoiceDate(invoice.createdAt) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else class="bg-white rounded-lg border shadow-sm p-8 text-center">
          <Icon name="lucide:file-text" class="w-12 h-12 text-gray-300 mx-auto mb-4"/>
          <h4 class="text-lg font-medium text-gray-900 mb-2">No Recent Activities</h4>
          <p class="text-gray-500 mb-4">Create your first invoice to see recent activities here.</p>
          <NuxtLink
            to="/invoices/Create/product"
            class="inline-flex items-center px-4 py-2 bg-[#00C951] text-white rounded-lg hover:bg-[#00B847] transition-colors"
          >
            <Icon name="lucide:plus" class="w-4 h-4 mr-2"/>
            Create Invoice
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useDashboardStore } from '~/stores/dashboard'
import { useInvoicesStore } from '~/stores/invoices'
import { useRouter } from 'vue-router'

const dashboardStore = useDashboardStore()
const invoicesStore = useInvoicesStore()
const router = useRouter()

// Get recent invoices (last 5)
const recentInvoices = computed(() => {
  return invoicesStore.invoices.slice(0, 5)
})

// Fetch dashboard and invoice data on component mount
onMounted(async () => {
  try {
    // Fetch dashboard data and recent invoices in parallel
    await Promise.all([
      dashboardStore.fetchDashboardData(),
      invoicesStore.fetchInvoices({ limit: 5, sort: '-createdAt' })
    ])
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
})

// Refresh dashboard data
const refreshDashboard = async () => {
  try {
    await Promise.all([
      dashboardStore.forceRefresh(),
      invoicesStore.fetchInvoices({ limit: 5, sort: '-createdAt' })
    ])
  } catch (error) {
    console.error('Failed to refresh dashboard:', error)
  }
}

// Navigation function removed - this is a display-only overview

// Helper function to get invoice status color
const getInvoiceStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    paid: 'bg-green-100 text-green-800',
    sent: 'bg-blue-100 text-blue-800',
    draft: 'bg-gray-100 text-gray-800',
    overdue: 'bg-red-100 text-red-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return colors[status.toLowerCase()] || 'bg-gray-100 text-gray-800'
}

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Helper function to format invoice date
const formatInvoiceDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return 'Today'
  } else if (diffDays === 2) {
    return 'Yesterday'
  } else if (diffDays <= 7) {
    return `${diffDays - 1} days ago`
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}
</script>
  
// composables/useApiClient.ts
import { useAuthStore } from '~/stores/auth'

interface ApiClientOptions {
  baseURL?: string
  timeout?: number
  retries?: number
}

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: string
}

class ApiClient {
  private baseURL: string
  private timeout: number
  private retries: number

  constructor(options: ApiClientOptions = {}) {
    const config = useRuntimeConfig()
    this.baseURL = options.baseURL || config.public.apiBaseUrl
    this.timeout = options.timeout || 30000
    this.retries = options.retries || 1
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const authStore = useAuthStore()

    // Ensure auth store is initialized
    if (!authStore.initialized) {
      await authStore.fetchUser()
    }

    // Get token from session
    const token = authStore.session?.access_token

    if (!token) {
      // Try to refresh the session first
      const refreshed = await authStore.refreshSession()
      const refreshedToken = authStore.session?.access_token

      if (!refreshed || !refreshedToken) {
        // Clear auth state and redirect to login
        await authStore.logout()
        throw new Error('Authentication required. Please log in again.')
      }

      return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${refreshedToken}`
      }
    }

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = authStore.session?.expires_at || 0

    if (expiresAt < now) {
      console.log('🔄 Token expired, attempting refresh...')
      const refreshed = await authStore.refreshSession()
      const refreshedToken = authStore.session?.access_token

      if (!refreshed || !refreshedToken) {
        await authStore.logout()
        throw new Error('Session expired. Please log in again.')
      }

      return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${refreshedToken}`
      }
    }

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000)
    const tokenExp = authStore.session?.expires_at || 0
    
    if (tokenExp < now) {
      console.log('🔄 Token expired, refreshing...')
      const refreshed = await authStore.refreshSession()
      if (!refreshed) {
        throw new Error('Token expired and refresh failed. Please log in again.')
      }
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authStore.session?.access_token}`
    }
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    let data: any
    
    try {
      data = await response.json()
    } catch (error) {
      data = { message: 'Invalid JSON response' }
    }

    if (!response.ok) {
      // Handle specific error codes
      if (response.status === 401) {
        // Token expired or invalid
        const authStore = useAuthStore()
        await authStore.logout()
        throw new Error('Authentication failed. Please log in again.')
      }
      
      if (response.status === 403) {
        throw new Error(data.message || 'Access denied. Insufficient permissions.')
      }
      
      if (response.status === 404) {
        throw new Error(data.message || 'Resource not found.')
      }
      
      if (response.status >= 500) {
        throw new Error(data.message || 'Server error. Please try again later.')
      }
      
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return data
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    requireAuth: boolean = true
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    
    let headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (requireAuth) {
      try {
        const authHeaders = await this.getAuthHeaders()
        headers = { ...headers, ...authHeaders }
      } catch (error) {
        console.error('Failed to get auth headers:', error)
        throw error
      }
    }

    const requestOptions: RequestInit = {
      ...options,
      headers,
      signal: AbortSignal.timeout(this.timeout)
    }

    let lastError: Error | null = null

    for (let attempt = 0; attempt <= this.retries; attempt++) {
      try {
        console.log(`🔗 API Request (attempt ${attempt + 1}):`, {
          method: options.method || 'GET',
          url,
          requireAuth
        })

        const response = await fetch(url, requestOptions)
        return await this.handleResponse<T>(response)
      } catch (error: any) {
        lastError = error
        
        // Don't retry on auth errors or client errors
        if (error.message.includes('Authentication failed') || 
            error.message.includes('Access denied') ||
            error.name === 'AbortError') {
          throw error
        }

        // Only retry on network errors or server errors
        if (attempt < this.retries && 
            (error.message.includes('fetch') || 
             error.message.includes('network') ||
             error.message.includes('Server error'))) {
          console.log(`⏳ Retrying request in ${(attempt + 1) * 1000}ms...`)
          await new Promise(resolve => setTimeout(resolve, (attempt + 1) * 1000))
          continue
        }

        throw error
      }
    }

    throw lastError || new Error('Request failed after retries')
  }

  // HTTP Methods
  async get<T>(endpoint: string, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET' }, requireAuth)
  }

  async post<T>(endpoint: string, data?: any, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    }, requireAuth)
  }

  async put<T>(endpoint: string, data?: any, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    }, requireAuth)
  }

  async patch<T>(endpoint: string, data?: any, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    }, requireAuth)
  }

  async delete<T>(endpoint: string, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE' }, requireAuth)
  }

  // Utility methods
  async testConnection(): Promise<boolean> {
    try {
      await this.get('/health', false)
      return true
    } catch (error) {
      console.error('API connection test failed:', error)
      return false
    }
  }

  async testAuth(): Promise<boolean> {
    try {
      await this.get('/auth-test/protected', true)
      return true
    } catch (error) {
      console.error('API auth test failed:', error)
      return false
    }
  }
}

// Create singleton instance
let apiClientInstance: ApiClient | null = null

export const useApiClient = (): ApiClient => {
  if (!apiClientInstance) {
    apiClientInstance = new ApiClient()
  }
  return apiClientInstance
}

// Export types
export type { ApiResponse, ApiClientOptions }

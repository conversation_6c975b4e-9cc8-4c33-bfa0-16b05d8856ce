import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useSupabaseClient, useRouter } from '#imports'
import type { User, Session } from '@supabase/supabase-js'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const session = ref<Session | null>(null)
  const loading = ref(false)
  const initialized = ref(false)
  const authStateListenerSetup = ref(false)

  const supabase = useSupabaseClient()
  const router = useRouter()

  // Computed properties
  const isAuthenticated = computed(() => !!user.value && !!session.value)
  const userEmail = computed(() => user.value?.email || '')
  const userName = computed(() => {
    if (!user.value) return ''

    // Try different metadata fields for name
    const metadata = user.value.user_metadata || {}
    const appMetadata = user.value.app_metadata || {}

    // Check various possible name fields
    return metadata.full_name ||
           metadata.name ||
           metadata.display_name ||
           appMetadata.full_name ||
           appMetadata.name ||
           `${metadata.first_name || ''} ${metadata.last_name || ''}`.trim() ||
           user.value.email?.split('@')[0] ||
           'User'
  })

  const fetchUser = async (): Promise<boolean> => {
    // Prevent multiple concurrent fetch operations
    if (loading.value) {
      console.log('🔄 Auth fetch already in progress, waiting...')
      // Wait for current operation to complete
      while (loading.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return !!session.value
    }

    try {
      loading.value = true
      console.log('🔄 Fetching user session...')

      // First try to get the session (which includes user data)
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        // Handle CORS or network errors gracefully
        if (sessionError.message.includes('CORS') || sessionError.message.includes('Failed to fetch')) {
          console.warn('⚠️ CORS/Network error, user not authenticated:', sessionError.message)
          user.value = null
          session.value = null
          return false
        }

        console.error('Error fetching session:', sessionError.message)
        user.value = null
        session.value = null
        return false
      }

      // If we have a session, extract user data
      if (sessionData?.session) {
        // Validate token expiration
        const now = Math.floor(Date.now() / 1000)
        const tokenExp = sessionData.session.expires_at || 0

        if (tokenExp < now) {
          console.log('🔄 Token expired, attempting refresh...')
          const refreshResult = await refreshSession()
          if (!refreshResult) {
            console.log('❌ Token refresh failed, clearing session')
            user.value = null
            session.value = null
            return false
          }
        } else {
          session.value = sessionData.session
          user.value = sessionData.session.user
        }

        // Debug user metadata
        console.log('👤 User authenticated:', {
          email: user.value?.email,
          expires_at: new Date((session.value?.expires_at || 0) * 1000).toISOString(),
          user_metadata: user.value?.user_metadata,
          app_metadata: user.value?.app_metadata
        })

        console.log('✅ User session loaded successfully')
        return true
      } else {
        // No session found
        console.log('❌ No active session found')
        user.value = null
        session.value = null
        return false
      }

    } catch (error: any) {
      // Handle network/CORS errors
      if (error.message?.includes('CORS') || error.message?.includes('Failed to fetch')) {
        console.warn('⚠️ Network/CORS error during auth check:', error.message)
      } else {
        console.error('Unexpected error fetching user:', error)
      }

      user.value = null
      session.value = null
      return false
    } finally {
      loading.value = false
      initialized.value = true
    }
  }

  const logout = async () => {
    try {
      loading.value = true

      // Try to sign out from Supabase, but handle CORS errors gracefully
      try {
        const { error } = await supabase.auth.signOut()

        if (error && !error.message.includes('CORS') && !error.message.includes('Failed to fetch')) {
          console.error('Error during logout:', error.message)
          throw error
        }
      } catch (error: any) {
        // Handle CORS errors gracefully
        if (error.message?.includes('CORS') || error.message?.includes('Failed to fetch')) {
          console.warn('⚠️ CORS error during logout, clearing local session only:', error.message)
          // Continue with local logout even if Supabase call fails
        } else {
          console.error('Unexpected logout error:', error)
          throw error
        }
      }

      // Always clear local state regardless of Supabase API success
      user.value = null
      session.value = null

      // Clear any stored tokens from localStorage
      if (import.meta.client) {
        try {
          localStorage.removeItem('supabase.auth.token')
          localStorage.removeItem('sb-' + 'szzsqpwwwkvsyyednaxm' + '-auth-token')
          sessionStorage.clear()
        } catch (e) {
          console.warn('Could not clear storage:', e)
        }
      }

      // Redirect to login page
      await router.push('/Auth/login')

      return true
    } catch (error: any) {
      console.error('Logout failed:', error)

      // Even if logout fails, clear local state and redirect
      user.value = null
      session.value = null

      if (import.meta.client) {
        try {
          localStorage.clear()
          sessionStorage.clear()
        } catch (e) {
          console.warn('Could not clear storage:', e)
        }
      }

      await router.push('/Auth/login')
      throw error
    } finally {
      loading.value = false
    }
  }

  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        console.error('Error refreshing session:', error.message)
        // If refresh fails, clear auth state
        user.value = null
        session.value = null
        return false
      }

      if (data?.session) {
        session.value = data.session
        user.value = data.session.user
        return true
      }

      return false
    } catch (error) {
      console.error('Unexpected error refreshing session:', error)
      return false
    }
  }

  // Setup auth state listener (only once)
  const setupAuthStateListener = () => {
    if (authStateListenerSetup.value) return

    authStateListenerSetup.value = true
    supabase.auth.onAuthStateChange(async (event, newSession) => {
      console.log('🔐 Auth Store - Auth state changed:', event, newSession?.user?.email)

      // Update session and user state
      session.value = newSession
      user.value = newSession?.user || null

      if (event === 'SIGNED_OUT') {
        user.value = null
        session.value = null
        console.log('🚪 User signed out, clearing auth state')
      } else if (event === 'SIGNED_IN') {
        user.value = newSession?.user || null
        session.value = newSession
        console.log('✅ User signed in:', newSession?.user?.email)
      } else if (event === 'TOKEN_REFRESHED') {
        user.value = newSession?.user || null
        session.value = newSession
        console.log('🔄 Token refreshed for:', newSession?.user?.email)
      }
    })
  }

  // Debug method to check auth status
  const debugAuthStatus = () => {
    console.log('🔍 Auth Debug Status:', {
      isAuthenticated: isAuthenticated.value,
      hasUser: !!user.value,
      hasSession: !!session.value,
      hasToken: !!session.value?.access_token,
      userEmail: user.value?.email,
      initialized: initialized.value,
      loading: loading.value
    })
  }

  return {
    // State
    user,
    session,
    loading,
    initialized,

    // Computed
    isAuthenticated,
    userEmail,
    userName,

    // Actions
    fetchUser,
    logout,
    refreshSession,
    setupAuthStateListener,
    debugAuthStatus
  }
})

<template>
  <div class=" mt-3 space-y-6">
    <!-- Client Table with Search & Filter -->
    <div class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      <!-- Search & Filter Bar -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <!-- search icon -->
        <div class="relative transition-all duration-300 ease-in-out">
          <div
            class="flex items-center border rounded-lg px-3 py-2 w-28 md:w-80 hover:w-56 hover:md:w-96 transition-all duration-300 ease-in-out bg-white shadow-sm focus-within:ring-2"
            style="--tw-ring-color: #05DF72"
          >
            <Icon name="lucide:search" class="w-5 h-5 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search invoices, clients..."
              class="ml-2 outline-none w-full bg-transparent text-sm text-gray-600"
            />
            <button
              v-if="searchQuery"
              class="ml-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear search"
            >
              <Icon name="lucide:x" class="w-4 h-4" />
            </button>
          </div>
        </div>

          <!-- Filter Dropdown -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline" class="w-full md:w-auto px-4 py-3">
                <Filter class="w-4 h-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="sortBy('recent')">Recent Clients</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('oldest')">Oldest Clients</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('Business')">Business Clients</DropdownMenuItem>
              <DropdownMenuItem @click="sortBy('Individual')">Individual Clients</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Client</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Type</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Client ID</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Contact</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Company</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Status</th>
              <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Created</th>
              <!-- <th class="px-8 py-4 text-left text-sm font-semibold text-gray-700">Actions</th> -->
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="!filteredClients.length" class="hover:bg-gray-50">
              <td colspan="8" class="px-8 py-12 text-center text-gray-500">
                <Icon name="lucide:users" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p class="text-lg font-medium">No clients found</p>
                <p class="text-sm">Try adjusting your search or filters</p>
              </td>
            </tr>
            <tr v-else v-for="client in filteredClients" :key="client._id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-8 py-6 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-[#00C951] flex items-center justify-center">
                      <span class="text-sm font-medium text-white">
                        {{ getInitials(client.name || 'U') }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ client.name }}</div>
                    <div class="text-sm text-gray-500">{{ client.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-8 py-6 whitespace-nowrap">
                <span class="px-3 py-1 inline-flex text-sm font-semibold rounded-full" :class="getTypeClass(client.type)">
                  {{ client.type || 'Individual' }}
                </span>
              </td>
              <td class="px-8 py-6 whitespace-nowrap">
                <div class="text-sm font-mono text-gray-900 font-medium">{{ client.clientId || 'Generating...' }}</div>
                <div class="text-xs text-gray-500">Auto-generated</div>
              </td>
              <td class="px-8 py-6 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ client.phone || 'N/A' }}</div>
                <div class="text-sm text-gray-500">{{ client.address?.city || 'N/A' }}</div>
              </td>
              <td class="px-8 py-6 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ client.company?.name || 'Individual' }}</div>
                <div class="text-sm text-gray-500">{{ client.company?.website || '' }}</div>
              </td>
              <td class="px-8 py-6 whitespace-nowrap">
                <span
                  class="px-3 py-1 inline-flex text-sm font-semibold rounded-full cursor-pointer hover:opacity-80 transition-opacity"
                  :class="getStatusClass(client.status)"
                  @click="toggleStatus(client)"
                  title="Click to toggle status"
                >
                  {{ client.status || 'Active' }}
                </span>
              </td>
              <td class="px-8 py-6 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(client.createdAt) }}
              </td>
              <!-- <td class="px-8 py-6 whitespace-nowrap">
                <div class="flex items-center gap-2">
                  <button
                    @click="editClient(client)"
                    class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit Client"
                  >
                    <Edit class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteClient(client)"
                    class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete Client"
                  >
                    <Trash class="w-4 h-4" />
                  </button>
                  <NuxtLink
                    :to="`/clients/${client._id}`"
                    class="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="View Details"
                  >
                    <Eye class="w-4 h-4" />
                  </NuxtLink>
                </div>
              </td> -->
            </tr>
          </tbody>
        </table>
      </div>

      <!-- View All Link -->
      <div class="px-8 py-4 bg-gray-50 border-t border-gray-200">
        <NuxtLink to="/clients/manage" class="text-sm font-medium text-blue-600 hover:text-blue-700 hover:underline">
          View All Clients →
        </NuxtLink>
      </div>
    </div>

    <!-- Edit Client Modal -->
    <ClientModal
      :open="showEditModal"
      :client="selectedClient"
      @close="closeEditModal"
      @client-saved="onClientUpdated"
    />

    <!-- Delete Confirmation Dialog -->
    <Dialog :open="showDeleteDialog" @update:open="setDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2 text-red-600">
            <Trash class="w-5 h-5" />
            Delete Client
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete <strong>{{ clientToDelete?.name }}</strong>?
            This action cannot be undone and will remove all associated data.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter class="flex gap-2 sm:gap-0">
          <button
            @click="setDeleteDialog(false)"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            @click="confirmDelete"
            :disabled="isDeleting"
            class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <Trash class="w-4 h-4" />
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Filter, Edit, Trash, Eye } from 'lucide-vue-next'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '~/components/ui/dialog'
import { useSearchManagement } from '~/composables/useSearchClear'
import { useClientStore } from '~/stores/clients'
import { useToast } from 'vue-toastification'
import ClientModal from './ClientModal.vue'

const props = defineProps<{ clients: any[] }>()
const searchQuery = ref('')
const sortOrder = ref('recent')
const { clearReactiveSearch } = useSearchManagement()

// Modal states
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const selectedClient = ref(null)
const clientToDelete = ref(null)
const isDeleting = ref(false)

// Store and toast
const clientStore = useClientStore()
const toast = useToast()

watch(() => props.clients, (newClients, oldClients) => {
  if (newClients && oldClients && newClients.length > oldClients.length) {
    if (searchQuery.value.includes('@')) {
      clearReactiveSearch(searchQuery)
    }
  }
}, { deep: true })

const filteredClients = computed(() => {
  if (!props.clients || !Array.isArray(props.clients)) return []

  let filtered = props.clients.filter(client => {
    const searchText = `${client.name || ''} ${client.email || ''} ${client.company?.name || ''} ${client.phone || ''}`
    return searchText.toLowerCase().includes(searchQuery.value.toLowerCase())
  })

  switch (sortOrder.value) {
    case 'Business':
      filtered = filtered.filter(c => c.type === 'Business')
      break
    case 'Individual':
      filtered = filtered.filter(c => c.type === 'Individual' || !c.type)
      break
    case 'oldest':
      filtered = filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      break
    case 'recent':
    default:
      filtered = filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
  }

  return filtered.slice(0, 10)
})

const getInitials = (name: string) => {
  return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Active': return 'bg-green-100 text-green-800'
    case 'Inactive': return 'bg-red-100 text-red-800'
    case 'Suspended': return 'bg-yellow-100 text-yellow-800'
    default: return 'bg-green-100 text-green-800'
  }
}

const getTypeClass = (type: string) => {
  switch (type) {
    case 'Business': return 'bg-blue-100 text-blue-800'
    case 'Individual':
    default: return 'bg-purple-100 text-purple-800'
  }
}

const formatDate = (date: string) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

function sortBy(order: string) {
  sortOrder.value = order
}

// Client management functions
const editClient = (client: any) => {
  selectedClient.value = client
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  selectedClient.value = null
}

const onClientUpdated = async () => {
  // Emit event to parent to refresh clients
  emit('client-updated')
  toast.success('Client updated successfully!')
  closeEditModal()
}

const deleteClient = (client: any) => {
  clientToDelete.value = client
  showDeleteDialog.value = true
}

const setDeleteDialog = (open: boolean) => {
  showDeleteDialog.value = open
  if (!open) {
    clientToDelete.value = null
  }
}

const confirmDelete = async () => {
  if (!clientToDelete.value) return

  try {
    isDeleting.value = true
    await clientStore.deleteClient(clientToDelete.value._id)
    toast.success(`Client "${clientToDelete.value.name}" deleted successfully!`)
    // Emit event to parent to refresh clients
    emit('client-deleted')
    setDeleteDialog(false)
  } catch (error: any) {
    toast.error('Failed to delete client: ' + error.message)
  } finally {
    isDeleting.value = false
  }
}

const toggleStatus = async (client: any) => {
  try {
    const currentStatus = client.status || 'Active'
    let newStatus = 'Active'

    // Cycle through statuses: Active -> Inactive -> Suspended -> Active
    if (currentStatus === 'Active') {
      newStatus = 'Inactive'
    
    } else {
      newStatus = 'Active'
    }

    await clientStore.updateClient(client._id, { status: newStatus })
    toast.success(`Client status changed to ${newStatus}`)
    // Emit event to parent to refresh clients
    emit('client-updated')
  } catch (error: any) {
    toast.error('Failed to update client status: ' + error.message)
  }
}

// Define emits
const emit = defineEmits(['client-updated', 'client-deleted'])
</script>

// plugins/auth-init.client.ts
export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()
  
  // Only run on client side
  if (process.client) {
    console.log('🔧 Auth initialization plugin starting...')
    
    try {
      // Initialize auth store and fetch user session
      await authStore.fetchUser()
      
      // Set up auth state listener after initialization
      authStore.setupAuthStateListener()
      
      console.log('✅ Auth initialization completed successfully')
    } catch (error) {
      console.error('❌ Auth initialization failed:', error)
      
      // On initialization failure, ensure we're in a clean state
      authStore.user = null
      authStore.session = null
      authStore.initialized = true
    }
  }
})

// middleware/auth-global.global.ts
import { useAuthStore } from '~/stores/auth'

/**
 * Global authentication middleware
 * Runs on every route change to ensure authentication state is properly managed
 */
export default defineNuxtRouteMiddleware(async (to, from) => {
  // Skip for server-side rendering
  if (process.server) {
    return
  }

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/Auth/login',
    '/Auth/register', 
    '/Auth/home',
    '/Auth/forgot-password',
    '/Auth/reset-password',
    '/Auth/verify-email',
    '/Auth/verify-otp'
  ]

  // Check if current route is public
  const isPublicRoute = publicRoutes.some(route => to.path.startsWith(route))

  const authStore = useAuthStore()

  try {
    // Always ensure auth store is initialized
    if (!authStore.initialized) {
      console.log('🔄 Global auth: Initializing auth store...')
      await authStore.fetchUser()
    }

    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (authStore.isAuthenticated && isPublicRoute) {
      console.log('✅ Global auth: Authenticated user accessing auth page, redirecting to dashboard')
      return navigateTo('/')
    }

    // If user is not authenticated and trying to access protected routes
    if (!authStore.isAuthenticated && !isPublicRoute) {
      console.log('❌ Global auth: Unauthenticated user accessing protected route, redirecting to login')
      
      // Store the intended destination for redirect after login
      const redirectTo = to.fullPath !== '/' ? to.fullPath : undefined
      const loginPath = redirectTo ? `/Auth/login?redirect=${encodeURIComponent(redirectTo)}` : '/Auth/login'
      
      return navigateTo(loginPath)
    }

    // For authenticated users on protected routes, check token validity
    if (authStore.isAuthenticated && !isPublicRoute) {
      if (authStore.session?.expires_at) {
        const now = Math.floor(Date.now() / 1000)
        const expiresAt = authStore.session.expires_at
        
        // If token is expired, try to refresh
        if (expiresAt < now) {
          console.log('🔄 Global auth: Token expired, attempting refresh...')
          
          try {
            const refreshed = await authStore.refreshSession()
            
            if (!refreshed) {
              console.log('❌ Global auth: Token refresh failed, redirecting to login')
              return navigateTo('/Auth/login')
            }
            
            console.log('✅ Global auth: Token refreshed successfully')
          } catch (error) {
            console.error('❌ Global auth: Error refreshing token:', error)
            return navigateTo('/Auth/login')
          }
        }
      }
    }

    console.log('✅ Global auth: Route access allowed for:', to.path)
  } catch (error) {
    console.error('❌ Global auth: Unexpected error:', error)
    
    // On error, redirect unauthenticated users to login
    if (!isPublicRoute) {
      return navigateTo('/Auth/login')
    }
  }
})
